import { FieldConfig } from "./FormGenerator";
import { Field } from "./types";

function transformSchemaToFieldConfig(field: Field): FieldConfig {
  const fieldConfig: FieldConfig = {
    name: field.name,
    type: field.type,
    widget: field.ui_config?.widget || getDefaultWidget(field.type),
    label: field.ui_config?.label || humanize(field.name),
    placeholder: field.ui_config?.placeholder,
    helpText: field.ui_config?.help_text,
    required: field.required,
    hidden: field.ui_config?.hidden || field.auto_generate || false,
    readonly: field.readonly || false,
    validation: field.validation,
    options: field.ui_config?.options,
    conditionalDisplay: field.ui_config?.conditional_display,
    min: field.validation?.min,
    max: field.validation?.max,
    grid_cols: field.ui_config?.grid_cols,
    targetComponent: undefined,
    displayField: field.ui_config?.display_field,
    foreignKey: field.foreign_key,
  };

  return fieldConfig;
}

function getDefaultWidget(fieldType: string): string {
  const widgetMap: Record<string, string> = {
    text: "text_input",
    "text[]": "text_array",
    uuid: "hidden",
    integer: "number_input",
    decimal: "number_input",
    boolean: "checkbox",
    timestamp: "datetime_input",
    date: "date_input",
    time: "time_input",
    enum: "select",
    json: "json_editor",
    jsonb: "json_editor",
  };
  return widgetMap[fieldType] || "text_input";
}

function humanize(str: string): string {
  return str
    ?.replace(/_/g, " ")
    ?.replace(/([a-z])([A-Z])/g, "$1 $2")
    ?.replace(/\b\w/g, (l: string) => l.toUpperCase());
}

export { transformSchemaToFieldConfig };
