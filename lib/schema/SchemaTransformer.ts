import { FieldConfig } from "./FormGenerator";
import { Field } from "./types";

function transformSchemaToFieldConfig(field: Field): FieldConfig {
  const fieldConfig: FieldConfig = {
    name: field.name,
    type: field.type,
    widget: field.ui_config?.widget || getDefaultWidget(field.type),
    label: field.ui_config?.label || humanize(field.name),
    placeholder: field.ui_config?.placeholder,
    helpText: field.ui_config?.help_text,
    required: field.required,
    hidden: field.ui_config?.hidden || field.auto_generate || false,
    readonly: field.readonly || false,
    validation: field.validation,
    options: field.ui_config?.options,
    conditionalDisplay: field.ui_config?.conditional_display,
    min: field.validation?.min,
    max: field.validation?.max,
    grid_cols: field.ui_config?.grid_cols,
    targetComponent: undefined,
    displayField: field.ui_config?.display_field,
    foreignKey: field.foreign_key,
  };

  return fieldConfig;
}

function getDefaultWidget(fieldType: string): string {
  const widgetMap: Record<string, string> = {